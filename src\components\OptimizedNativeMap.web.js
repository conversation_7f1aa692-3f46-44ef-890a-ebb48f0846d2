import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Platform, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Import constants
import Colors from '../constants/Colors';

/**
 * OptimizedNativeMap Component - Web Version
 * 
 * This is a web-specific version that doesn't import react-native-maps
 * and provides a fallback UI for web platforms.
 */

const OptimizedNativeMap = ({
  location,
  style,
  onMapPress,
  onDirectionsPress,
  showStyleButton = true,
  showDirectionsButton = true,
  ...props
}) => {
  const [styleButtonPressed, setStyleButtonPressed] = useState(false);
  const [directionsButtonPressed, setDirectionsButtonPressed] = useState(false);
  const [currentMapType, setCurrentMapType] = useState({ name: 'Standard', key: 'standard' });

  // Map type options for web
  const mapTypes = [
    { name: 'Standard', key: 'standard' },
    { name: 'Satellite', key: 'satellite' },
    { name: 'Terrain', key: 'terrain' },
  ];

  // Handle map press
  const handleMapPress = useCallback(() => {
    if (onMapPress) {
      onMapPress();
    } else {
      // Default behavior - open in Google Maps
      const query = encodeURIComponent(location.address);
      const url = `https://www.google.com/maps/search/?api=1&query=${query}`;
      window.open(url, '_blank');
    }
  }, [onMapPress, location]);

  // Handle style button press
  const handleStylePress = useCallback(() => {
    const currentIndex = mapTypes.findIndex(type => type.key === currentMapType.key);
    const nextIndex = (currentIndex + 1) % mapTypes.length;
    setCurrentMapType(mapTypes[nextIndex]);
  }, [currentMapType, mapTypes]);

  // Handle directions button press
  const handleDirectionsPress = useCallback(() => {
    if (onDirectionsPress) {
      onDirectionsPress();
    } else {
      // Default behavior - open in Google Maps with directions
      const query = encodeURIComponent(location.address);
      const url = `https://www.google.com/maps/dir/?api=1&destination=${query}`;
      window.open(url, '_blank');
    }
  }, [onDirectionsPress, location]);

  // Render web fallback map
  const renderWebMap = () => (
    <TouchableOpacity
      onPress={handleMapPress}
      activeOpacity={0.9}
      style={styles.mapImageContainer}
    >
      <View style={[styles.map, styles.webMap]}>
        <Ionicons name="location" size={48} color={Colors.primary} />
        <Text style={styles.webMapTitle}>Click to view in Google Maps</Text>
        <Text style={styles.webMapSubtext}>{location.address}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Map Title */}
      <Text style={styles.mapTitle}>{location.address}</Text>
      
      {/* Style Indicator */}
      <View style={styles.statusContainer}>
        <Text style={styles.mapStyleIndicator}>
          Map Style: {currentMapType.name}
        </Text>
        <Text style={styles.mapProviderIndicator}>
          (Web Version)
        </Text>
      </View>

      {/* Map Container */}
      {renderWebMap()}

      {/* Map Controls */}
      <View style={styles.mapButtonsContainer}>
        {showStyleButton && (
          <TouchableOpacity
            style={[
              styles.mapStyleButton,
              styleButtonPressed && styles.mapStyleButtonPressed
            ]}
            onPress={handleStylePress}
            onPressIn={() => setStyleButtonPressed(true)}
            onPressOut={() => setStyleButtonPressed(false)}
            activeOpacity={1}
          >
            <Ionicons name="layers" size={16} color={Colors.white} style={{ marginRight: 6 }} />
            <Text style={[
              styles.mapButtonText,
              styleButtonPressed && styles.mapButtonTextPressed
            ]}>
              Change Style
            </Text>
          </TouchableOpacity>
        )}

        {showDirectionsButton && (
          <TouchableOpacity
            style={[
              styles.mapDirectionsButton,
              directionsButtonPressed && styles.mapDirectionsButtonPressed
            ]}
            onPress={handleDirectionsPress}
            onPressIn={() => setDirectionsButtonPressed(true)}
            onPressOut={() => setDirectionsButtonPressed(false)}
            activeOpacity={1}
          >
            <Text style={[
              styles.mapDirectionsButtonText,
              directionsButtonPressed && styles.mapDirectionsButtonTextPressed
            ]}>
              Get Directions
            </Text>
            <Ionicons name="navigate" size={16} color={Colors.white} style={{ marginLeft: 6 }} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 18,
    borderWidth: 2,
    borderColor: '#b7c9c9',
    padding: 12,
    marginHorizontal: 0,
    marginBottom: 0,
    alignItems: 'center',
  },
  mapTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2a5d6b',
    marginBottom: 4,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  mapStyleIndicator: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
    textTransform: 'capitalize',
    marginRight: 4,
  },
  mapProviderIndicator: {
    fontSize: 10,
    color: '#888',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  mapImageContainer: {
    width: '100%',
    height: 180,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.lightGray,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  webMap: {
    backgroundColor: Colors.sectionBackground,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#b7c9c9',
  },
  webMapTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2a5d6b',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 4,
  },
  webMapSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  mapButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    width: '100%',
  },
  mapStyleButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
    justifyContent: 'center',
  },
  mapStyleButtonPressed: {
    backgroundColor: Colors.primaryDark,
    transform: [{ scale: 0.98 }],
  },
  mapButtonText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  mapButtonTextPressed: {
    color: Colors.lightGray,
  },
  mapDirectionsButton: {
    backgroundColor: Colors.accent,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
    justifyContent: 'center',
  },
  mapDirectionsButtonPressed: {
    backgroundColor: Colors.accentDark,
    transform: [{ scale: 0.98 }],
  },
  mapDirectionsButtonText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  mapDirectionsButtonTextPressed: {
    color: Colors.lightGray,
  },
});

export default OptimizedNativeMap;
