/**
 * App color constants - Improved Golden & Black Theme
 * Golden yellow is used for accents, buttons, and decorative elements only
 * All text and headings use black or dark grey for better readability
 */
export default {
  // Primary golden yellow palette (for accents, buttons, decorative elements)
  primary: '#FAB52C',           // Main golden yellow - for buttons & accents
  primaryDark: '#E09B1A',       // Darker golden for hover states
  primaryLight: '#FCC55A',      // Lighter golden for subtle accents
  
  // Secondary black and gray palette
  secondary: '#1a1a1a',         // Deep black for secondary elements
  secondaryLight: '#333333',    // Medium gray-black
  
  // Background colors
  background: '#fff',           // Main white background
  cardBackground: '#fafafa',    // Very light gray for cards
  sectionBackground: '#f8f8f8', // Light gray for sections
  
  // Text colors (black and dark grey only - NO yellow text)
  text: '#1a1a1a',              // Primary black text
  textSecondary: '#4a4a4a',     // Secondary gray text
  textLight: '#6b6b6b',         // Light gray text
  textMuted: '#999999',         // Muted gray text
  heading: '#1a1a1a',           // Black for headings
  headingSecondary: '#2a2a2a',  // Dark grey for secondary headings
  
  // Standard colors
  white: 'white',
  black: '#000000',
  
  // Gray scale
  gray: '#e5e5e5',
  lightGray: '#f5f5f5',
  mediumGray: '#cccccc',
  darkGray: '#666666',
  
  // Status colors
  error: '#D32F2F',
  success: '#52c41a',
  warning: '#FAB52C',           // Using primary golden for warnings
  
  // Utility colors
  transparent: 'transparent',
  overlay: 'rgba(0,0,0,0.5)',
  formBackground: 'rgba(26, 26, 26, 0.85)',
  
  // Enhanced overlay colors for better contrast
  heroOverlay: 'rgba(0, 0, 0, 0.65)',
  heroOverlayLight: 'rgba(0, 0, 0, 0.45)',
  contactOverlay: 'rgba(0, 0, 0, 0.85)',
  playButtonOverlay: 'rgba(0, 0, 0, 0.9)',
  
  // High contrast text colors for overlays
  overlayText: '#ffffff',
  overlayTextSecondary: '#f0f0f0',
  overlayTextAccent: '#FAB52C',     // Using golden for accent text
  
  // Accessible color combinations
  accessiblePrimary: '#E09B1A',     // Darker golden for better contrast
  accessibleSecondary: '#1a1a1a',   // Black for high contrast
  
  // Button states
  buttonPrimary: '#FAB52C',
  buttonPrimaryHover: '#E09B1A',
  buttonPrimaryPressed: '#1a1a1a',
  buttonSecondary: '#1a1a1a',
  buttonSecondaryHover: '#333333',
  
  // Border colors
  border: '#e5e5e5',
  borderLight: '#f0f0f0',
  borderDark: '#cccccc',
  
  // Golden accent variations
  accent: '#FAB52C',
  accentLight: '#FCC55A',
  accentDark: '#E09B1A',
  accentSubtle: 'rgba(250, 181, 44, 0.1)',
};
