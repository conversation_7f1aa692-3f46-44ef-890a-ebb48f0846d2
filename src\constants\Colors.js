/**
 * App color constants - Modern Medical UI Design System
 * Professional healthcare color palette with improved accessibility and visual hierarchy
 * Combines medical trust (blues) with warmth (accent colors) for optimal user experience
 */
export default {
  // Primary brand colors - Modern medical blue palette
  primary: '#2563EB',           // Professional medical blue - primary actions
  primaryDark: '#1D4ED8',       // Darker blue for hover states
  primaryLight: '#3B82F6',      // Lighter blue for subtle accents
  primarySoft: '#EFF6FF',       // Very light blue for backgrounds

  // Secondary accent colors - Warm and approachable
  secondary: '#059669',         // Medical green for success/health
  secondaryDark: '#047857',     // Darker green for emphasis
  secondaryLight: '#10B981',    // Lighter green for highlights

  // Tertiary accent - Professional orange for CTAs
  accent: '#EA580C',            // Medical orange for important actions
  accentDark: '#C2410C',        // Darker orange for hover
  accentLight: '#FB923C',       // Lighter orange for backgrounds
  accentSoft: '#FFF7ED',        // Very light orange background

  // Neutral color system - Modern grays
  neutral50: '#F8FAFC',         // Lightest background
  neutral100: '#F1F5F9',        // Light background
  neutral200: '#E2E8F0',        // Border light
  neutral300: '#CBD5E1',        // Border medium
  neutral400: '#94A3B8',        // Text muted
  neutral500: '#64748B',        // Text secondary
  neutral600: '#475569',        // Text primary
  neutral700: '#334155',        // Text emphasis
  neutral800: '#1E293B',        // Text strong
  neutral900: '#0F172A',        // Text strongest

  // Background colors - Layered system
  background: '#FFFFFF',        // Main white background
  backgroundSoft: '#F8FAFC',    // Soft background for sections
  cardBackground: '#FFFFFF',    // Card backgrounds with shadows
  surfaceElevated: '#FFFFFF',   // Elevated surfaces

  // Text colors - Semantic and accessible
  text: '#334155',              // Primary text (neutral-700)
  textSecondary: '#64748B',     // Secondary text (neutral-500)
  textMuted: '#94A3B8',         // Muted text (neutral-400)
  textLight: '#CBD5E1',         // Light text (neutral-300)
  heading: '#1E293B',           // Headings (neutral-800)
  headingSecondary: '#475569',  // Secondary headings (neutral-600)

  // Standard colors
  white: '#FFFFFF',
  black: '#000000',

  // Legacy gray scale (for backward compatibility)
  gray: '#E2E8F0',              // neutral-200
  lightGray: '#F1F5F9',         // neutral-100
  mediumGray: '#CBD5E1',        // neutral-300
  darkGray: '#64748B',          // neutral-500

  // Status colors - Medical appropriate
  error: '#DC2626',             // Red-600 for errors
  errorLight: '#FEF2F2',        // Red-50 for error backgrounds
  success: '#059669',           // Green-600 for success (matches secondary)
  successLight: '#ECFDF5',      // Green-50 for success backgrounds
  warning: '#D97706',           // Amber-600 for warnings
  warningLight: '#FFFBEB',      // Amber-50 for warning backgrounds
  info: '#2563EB',              // Blue-600 for info (matches primary)
  infoLight: '#EFF6FF',         // Blue-50 for info backgrounds

  // Utility colors
  transparent: 'transparent',
  overlay: 'rgba(15, 23, 42, 0.6)',        // neutral-900 with opacity
  overlayLight: 'rgba(15, 23, 42, 0.4)',   // Lighter overlay
  formBackground: 'rgba(30, 41, 59, 0.95)', // neutral-800 with opacity

  // Enhanced overlay colors for hero sections
  heroOverlay: 'rgba(15, 23, 42, 0.7)',    // Strong overlay for readability
  heroOverlayLight: 'rgba(15, 23, 42, 0.5)', // Lighter overlay
  contactOverlay: 'rgba(15, 23, 42, 0.8)',  // Strong overlay for forms

  // High contrast text colors for overlays
  overlayText: '#FFFFFF',
  overlayTextSecondary: '#F1F5F9',          // neutral-100
  overlayTextAccent: '#3B82F6',             // primaryLight for accent text

  // Button states - Modern and accessible
  buttonPrimary: '#2563EB',                 // primary
  buttonPrimaryHover: '#1D4ED8',            // primaryDark
  buttonPrimaryPressed: '#1E40AF',          // Even darker for pressed state
  buttonSecondary: '#059669',               // secondary
  buttonSecondaryHover: '#047857',          // secondaryDark
  buttonTertiary: '#EA580C',                // accent
  buttonTertiaryHover: '#C2410C',           // accentDark

  // Border colors - Subtle and modern
  border: '#E2E8F0',            // neutral-200
  borderLight: '#F1F5F9',       // neutral-100
  borderMedium: '#CBD5E1',      // neutral-300
  borderDark: '#94A3B8',        // neutral-400
  borderFocus: '#3B82F6',       // primaryLight for focus states

  // Surface colors for cards and elevated elements
  surface: '#FFFFFF',
  surfaceHover: '#F8FAFC',      // neutral-50
  surfacePressed: '#F1F5F9',    // neutral-100
  surfaceDisabled: '#F8FAFC',   // neutral-50
};
